import { Injectable } from '@angular/core'
import { SessaoService } from '@common/services/sessao.service'
import { environment } from '@environments/environment'
import { UserDataInterface } from '@guards/services/user-data'
import { CrossStorageClient } from 'cross-storage'
import { first } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class UserDataService {
  public storage: CrossStorageClient = new CrossStorageClient(
    `${environment.backoOfficeUrl}`,
  )
  public iframe = this.storage
  public promisse = this.storage.onConnect()
  private _userData: UserDataInterface
  private _theme: string
  private _themeDev: string

  public get userData(): UserDataInterface {
    return this._userData
  }

  public set userData(value: UserDataInterface) {
    this._userData = value
  }

  public get theme(): string {
    return this._theme
  }

  public set theme(value: string) {
    this._theme = value
  }

  public get themeDev(): string {
    return this._themeDev
  }

  public set themeDev(value: string) {
    this._themeDev = value
  }

  constructor(private service: SessaoService) {}

  public getUserData(): any {
    return JSON.parse(localStorage.getItem('userData'))
  }

  public async recuperarToken(idToken: string): Promise<void> {
    this.service
      .recuperaSessao(idToken)
      .pipe(first())
      .subscribe(sessaoSalva => {
        localStorage.removeItem('userData')

        localStorage.setItem('userData', JSON.stringify(sessaoSalva.dados))
      })
  }

  public crossClientGet(key: string): any {
    console.log('crossClientGet.key', key);
    const tokenFake =
      '{"idToken":"59a33b69-047c-4844-be56-95c4c9928082","entidadeUuid":"167b4fce-7b4d-4b3d-bb2e-b0cd4744ecd3","municipioClienteUuid":"ac94a58b-1a5c-4c3b-8956-a2c006a1c389","exercicioUuid":"40306b62-bd63-4724-8e45-32a47fa8a34a","exercicio":2025,"clienteUuid":"db336bb3-e9e0-4918-aac4-317b44a92057","cpf":"admin","email":"<EMAIL>","entidade":{"uuid":"167b4fce-7b4d-4b3d-bb2e-b0cd4744ecd3","clienteUuid":"db336bb3-e9e0-4918-aac4-317b44a92057","codigo":350,"nome":"Prefeitura Municipal de Alvorada do Sul","uf":"PR","brasao":{"uuid":"7259323b-e5a7-4561-8b8c-9e929ccb7932","nome":"8891fbcc-f014-4958-acd9-f224b728b22a.png","tipo_arquivo":"image/png","identificadorUuid":"BLAZON167b4fce-7b4d-4b3d-bb2e-b0cd4744ecd3","link":"https://objectstorage.sa-saopaulo-1.oraclecloud.com/p/rm0U0DsgxLUfyvhw_ThFTYWH_YCoFXa2oen4fDujuEqdbuxbwuZx8FKZPBDBx8Vn/n/gr3m6keec5b3/b/api-anexo/o/upload/homolog/8891fbcc-f014-4958-acd9-f224b728b22a.png","descricao":"BLAZON","publicaInternet":"NO","etag":"273fe12e-6559-476f-b49e-20da2b52e880","conteudo":"1nfw5bvfFJRTke5blzTFDw==","requisicaoId":"gru-1:gkvT_ZYaGOSrMMFxPqdV86CQ5yAYx9sl1PaUm3AoxnpDP-dBzwa4qrz9bQ-147cw"}},"nome":"admin","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ISy1RfcOCV-TVjGlZ6ThNm5v3GySQHPvQPOpJw5sjHI","tokenJwt":"eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BFBkukRclcqysoCCE2XXeTEw50j612wqyHWrS7spuJmhMd0Rq2oVBi6Urb3m6QMCIBseJDPgFcRjRmIyzZbs1A","uuid":"664f8da6-f13f-4ba6-8bbe-ede27f852b97","exercise":{"status":{"id":4,"uuid":"*************-4115-9bf2-62e1c95e8909","codigo":4,"nome":"Execução"},"texto":"2025","valor":"40306b62-bd63-4724-8e45-32a47fa8a34a"}}';
    return (
      this.promisse
        // .then(() => this.iframe.get(key))
        .then(() =>
          key == 'userData'
            ? tokenFake
            : key == 'theme'
            ? 'dark'
            : 'material.nebular.dark',
        )
        .then((data: string) => {
          if (key === 'userData') {
            const userData = JSON.parse(data);
            this.userData = userData;
          } else if (key === 'theme') {
            this.theme = data;
          } else if (key === 'devExtremeTheme') {
            this.themeDev = data;
          }
          localStorage.setItem(key, data);
          if (
            key !== 'userData' ||
            (this.userData &&
              this.userData !== undefined &&
              this.userData !== null)
          ) {
            return true;
          } else {
            const url = window.location.href;
            let prefix: string = '/#/login';
            // window.open(
            //   `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(
            //     url,
            //   )}`,
            //   '_self',
            // );
          }
        })
        .catch(() => {
          if (key === 'userData') {
            const url = window.location.href;
            let prefix: string = '/#/login';
            // window.open(
            //   `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(
            //     url,
            //   )}`,
            //   '_self',
            // );
          }
        })
    );
  }

  public crossClientSet(key: string, value: any): any {
    return this.promisse
      .then(() =>
        this.iframe.set(
          key,
          typeof value === 'string' ? value : JSON.stringify(value),
        ),
      )
      .then((data: string) => {
        if (key === 'userData') {
          const userData = JSON.parse(data)
          this.userData = userData
        } else if (key === 'theme') {
          this.theme = data
        } else if (key === 'devExtremeTheme') {
          this.themeDev = data
        }

        localStorage.setItem(key, data)

        if (data && data !== undefined && data !== null) {
          return true
        } else return false
      })
      .catch(() => {
        // do something
      })
  }

  public crossClientDel(key: string): any {
    return this.promisse
      .then(() => this.iframe.del(key))
      .then((data: string) => {
        localStorage.removeItem(key)
        if (key === 'userData') {
          console.log('sessao token delete')
          const url = window.location.href
          let prefix: string = '/#/login'
          window.open(
            `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(url)}`,
            '_self',
          )
        }
      })
      .catch(() => {})
  }
}
