import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ReportsComponent } from './reports.component';
import { SurplusResourceSourceComponent } from './surplus-resource-source/surplus-resource-source.component';
import { RelatorioOrcamentoCriancaComponent } from './relatorio-orcamento-crianca/relatorio-orcamento-crianca.component';

const routes: Routes = [
  {
    path: '',
    component: ReportsComponent,
  },
  {
    path: 'demonstrativo-superavit-fonte-recurso',
    component: SurplusResourceSourceComponent,
  },
  {
    path: 'demonstrativo-contas-consignacao',
    loadChildren: () =>
      import(
        './statement-consignment-account/statement-consignment-account.module'
      ).then((m) => m.StatementConsignmentAccountModule),
  },
  {
    path: 'demonstrativo-contas-realizavel',
    loadChildren: () =>
      import(
        './statement-realizable-account/statement-realizable-account.module'
      ).then((m) => m.StatementRealizableAccountModule),
  },
  {
    path: 'retencao-pagamento',
    loadChildren: () =>
      import('./payment-retention/payment-retention.module').then(
        (m) => m.PaymentRetentionModule,
      ),
  },
  {
    path: 'consolidacao-despesa',
    loadChildren: () =>
      import(
        './expense-consolidation-function-subfunction/expense-consolidation-function-subfunction.module'
      ).then((m) => m.ExpenseConsolidationFunctionSubfunctionModule),
  },
  {
    path: 'situacao-orcamentaria-despesa',
    loadChildren: () =>
      import('./expense-budget-situation/expense-budget-situation.module').then(
        (m) => m.ExpenseBudgetSituationModule,
      ),
  },
  {
    path: 'execucao-orcamentaria-contratos',
    loadChildren: () =>
      import(
        './contract-budget-execution-report/contract-budget-execution-report.module'
      ).then((m) => m.ContractBudgetExecutionReportModule),
  },
  {
    path: 'consolidacao-receita',
    loadChildren: () =>
      import(
        './consolidated-management-revenue/consolidated-management-revenue.module'
      ).then((m) => m.ConsolidatedManagementRevenueModule),
  },
  {
    path: 'saldo-despesa-orcamentaria-executada',
    loadChildren: () =>
      import(
        './balance-executed-budget-expenditure/balance-executed-budget-expenditure.module'
      ).then((m) => m.BalanceExecutedBudgetExpenditureModule),
  },
  {
    path: 'saldo-conta-despesa',
    loadChildren: () =>
      import('./expense-account-balance/expense-account-balance.module').then(
        (m) => m.ExpenseAccountBalanceModule,
      ),
  },
  {
    path: 'razao-conta-despesa',
    loadChildren: () =>
      import('./reason-expense-account/reason-expense-account.module').then(
        (m) => m.ReasonExpenseAccountModule,
      ),
  },
  {
    path: 'razao-execucao-contratos',
    loadChildren: () =>
      import(
        './reason-for-executing-contracts/reason-for-executing-contracts.module'
      ).then((m) => m.ReasonForExecutingContractsModule),
  },
  {
    path: 'situacao-orcamentaria-receita',
    loadChildren: () =>
      import('./revenue-budget-situation/revenue-budget-situation.module').then(
        (m) => m.RevenueBudgetSituationModule,
      ),
  },
  {
    path: 'extrato-divida-fundada',
    loadChildren: () =>
      import('./divided-debt-statement/divided-debt-statement.module').then(
        (m) => m.DividedDebtStatementModule,
      ),
  },
  {
    path: 'controle-entrada-quantitativa-antes-liquidacao',
    loadChildren: () =>
      import(
        './quantitative-entry-control-before-liquidation/quantitative-entry-control-before-liquidation.module'
      ).then((m) => m.QuantitativeEntryControlBeforeLiquidationModule),
  },
  {
    path: 'restos-pagar-consolidados',
    loadChildren: () =>
      import(
        './consolidated-balances-payable/consolidated-balances-payable.module'
      ).then((m) => m.ConsolidatedBalancesPayableModule),
  },
  {
    path: 'empenho-emitido',
    loadChildren: () =>
      import('./effort-issued-report/effort-issued-report.module').then(
        (m) => m.EffortIssuedReportModule,
      ),
  },
  {
    path: 'estorno-empenho',
    loadChildren: () =>
      import(
        './reversal-effort-issued-report/reversal-effort-issued-report.module'
      ).then((m) => m.ReversalEffortIssuedReportModule),
  },
  {
    path: 'reversao-estorno-empenho',
    loadChildren: () =>
      import(
        './revert-reversal-effort-issued-report/revert-reversal-effort-issued-report.module'
      ).then((m) => m.RevertReversalEffortIssuedReportModule),
  },
  {
    path: 'restos-pagar-consolidados',
    loadChildren: () =>
      import(
        './consolidated-balances-payable/consolidated-balances-payable.module'
      ).then((m) => m.ConsolidatedBalancesPayableModule),
  },
  {
    path: 'receita-arrecadada',
    loadChildren: () =>
      import('./revenue-collected/revenue-collected.module').then(
        (m) => m.RevenueCollectedModule,
      ),
  },
  {
    path: 'liquidacao',
    loadChildren: () =>
      import('./liquidation-report/liquidation-report.module').then(
        (m) => m.LiquidationReportModule,
      ),
  },
  {
    path: 'estorno-liquidacao',
    loadChildren: () =>
      import(
        './reversal-liquidation-report/reversal-liquidation-report.module'
      ).then((m) => m.ReversalLiquidationReportModule),
  },
  {
    path: 'previsoes-ou-pagamentos',
    loadChildren: () =>
      import(
        './forecasts-or-payments/forecasts-or-payments-report.module'
      ).then((m) => m.ForecastsOrPaymentsReportModule),
  },
  {
    path: 'estorno-pagamentos',
    loadChildren: () =>
      import('./reversal-payments/reversal-payments-report.module').then(
        (m) => m.ReversalPaymentsReportModule,
      ),
  },
  {
    path: 'saldo-despesa-orgao',
    loadChildren: () =>
      import('./organ-expense-balance/organ-expense-balance.module').then(
        (m) => m.OrganExpenseBalanceModule,
      ),
  },
  {
    path: 'demonstrativo-percentual-gasto',
    loadChildren: () =>
      import('./statement-of-the-percentage-spent/statement-of-the-percentage-spent.module').then(
        (m) => m.StatementOfThePercentageSpentModule,
      ),
  },
  {
    path: 'empenho-servidor',
    loadChildren: () =>
      import('./relatorio-empenho-servidor/relatorio-empenho-servidor.module').then(
        (m) => m.RelatorioEmpenhoServidorModule,
      ),
  },
  {
    path: 'consolidacao-despesa-natureza',
    loadChildren: () =>
      import('./relatorio-consolidacao-despesa-natureza/relatorio-consolidacao-despesa-natureza.module').then(
        (m) => m.RelatorioConsolidacaoDespesaNaturezaModule,
      )
  },
  {
    path: 'movimento-consignacao',
    loadChildren: () =>
      import('./relatorio-movimento-consignacoes/relatorio-movimento-consignacoes.module').then(
        (m) => m.RelatorioMovimentoConsignacoesModule,
      ),
  },
  {
    path: 'demonstrativo-excesso-arrecadacao',
    loadChildren: () =>
      import('./relatorio-demonstrativo-excesso-arrecadacao/relatorio-demonstrativo-excesso-arrecadacao.module').then(
        (m) => m.RelatorioDemonstrativoExcessoArrecadacaoModule,
      ),
  },
  {
    path: 'orcamento-crianca',
    loadChildren: () => import('./relatorio-orcamento-crianca/relatorio-orcamento-crianca.module')
      .then(m => m.RelatorioOrcamentoCriancaModule)
      },
  {
    path: 'notas',
    loadChildren: () =>
      import('./notas/notas.module').then(
        (m) => m.NotasModule,
      ),
  },
];



@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReportsRoutingModule {}

