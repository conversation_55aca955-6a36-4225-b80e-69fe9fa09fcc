import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component';
import { SendDocumentComponent } from '@common/dialogs/send-document/send-document.component';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogService } from '@nebular/theme';
import { EffortStatementComponent } from '@pages/expense/effort-statement/effort-statement.component';
import { MenuService } from '@pages/menu.service';
import { RelatorioEmpenhoServidorComponent } from '@pages/reports/relatorio-empenho-servidor/relatorio-empenho-servidor.component';
import { NoteEmissionFiltersComponent } from '@pages/shared/dialogs/note-emission-filters/note-emission-filters.component';
import { currencyFormat } from '@pages/shared/helpers/format.helper';
import { DataSourceHelperService } from '@pages/shared/services/data-source-helper.service';
import DataSource from 'devextreme/data/data_source';
import { Column } from 'devextreme/ui/data_grid';
import { finalize, take } from 'rxjs/operators';
import { EffortInterface } from '../../interfaces/effort';
import { EffortService } from '../../services/effort.service';
import { EffortFollowDialogComponent } from '../effort-follow-dialog/effort-follow-dialog.component';
import { EffortQueueComponent } from '../effort-queue/effort-queue.component';

@Component({
  selector: 'eqp-effort-list',
  templateUrl: './effort-list.component.html',
  styleUrls: ['./effort-list.component.scss'],
})
export class EffortListComponent extends BaseTelasComponent implements OnInit {
  public loading: boolean = false;
  public pageTitle = 'Empenho';

  currencyFormat = currencyFormat;
  dataSource: DataSource;

  public previsaoInicialDespesaFonteData: any;
  public planoDespesaData: any;
  public fornecedorData: any;
  public action: 'empty' | 'pending' | 'completed' = 'empty'
  public habilitarCadastro: boolean = false

  constructor(
    public menuService: MenuService,
    public router: Router,
    public toastr: ToastrService,
    private service: EffortService,
    private dialogService: NbDialogService,
    private dataSourceHelperService: DataSourceHelperService,
  ) {
    super(menuService, router);
    this.permissao('/despesa/empenho');
  }

  ngOnInit(): void {
    this.previsaoInicialDespesaFonteData = {
      store: this.service.getDataSource(
        'uuid',
        'previsao_inicial_despesa_fonte/paginado',
        10,
      ),
      pageSize: 10,
      paginate: true,
    };

    this.planoDespesaData = {
      store: this.service.getDataSourceFiltro(
        'uuid',
        'plano_despesa/paginado',
        10,
      ),
      pageSize: 10,
      paginate: true,
    };

    this.fornecedorData = {
      store: this.service.getDataSourceFiltro('uuid', 'fornecedor', 10),
      pageSize: 10,
      paginate: true,
    };

    this.fetchGrid();
  }

  effortDisplay(item) {
    return item && `${item.numero}/${item.anoCompetencia}`;
  }

  customDisplay(item) {
    return item && `${item.pessoaCodigo} - ${item.pessoaNome}`;
  }

  decreeDispay(item) {
    return item && `${item.codigo} - ${item.descricaoTipoDocumento}`;
  }

  public fetchGrid(): void {
    this.dataSource = this.dataSourceHelperService.getDataSource(
      'despesa_empenho/reduzido',
      'uuid',
      10,
      'numero',
    );
  }

  public novoRegistro(): void {
    this.gravarParametros();
    this.router.navigate([`despesa/empenho/novo`]);
  }

  public edit(value) {
    this.gravarParametros();
    this.router.navigate([`despesa/empenho/edit/${value}`]);
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever';
          item.options.text = 'Empenho';
          item.options.hint = 'Cadastrar empenho';
          item.options.onClick = () => this.novoRegistro();
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          };
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          };
        }
      });
    }
  }

  public print(uuid: any): void {
    this.dialogService.open(NoteEmissionFiltersComponent, {
      context: {
        uuid: uuid,
        currentScreen: 'EMPENHO'
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    });
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    });

    dialogRef.onClose.subscribe((res) => {
      if (res === 'S') {
        this.service
          .delete(uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            (_) => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Empenho removido(a) com sucesso',
              });
              this.fetchGrid();
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          );
      }
    });
  }

  public openEffortQueue() {
    const dialogRef = this.dialogService.open(EffortQueueComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    });

    dialogRef.onClose.pipe(take(1)).subscribe((_) => {
      this.fetchGrid();
    });
  }

  public openEffortFollow(empenho: EffortInterface) {
    const dialogRef = this.dialogService.open(EffortFollowDialogComponent, {
      context: {
        empenhoUuid: empenho.uuid,
        empenho: empenho,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    });

    dialogRef.onClose.pipe(take(1)).subscribe((_) => {
      this.fetchGrid()
    });
  }

  public sendDocument() {
    const ref = this.dialogService.open(SendDocumentComponent, {
      context: {
        dialogTitle: 'Confirma o envio do documento para assinatura?',
        dialogSize: 'medium'
      },
      closeOnBackdropClick: false,
      closeOnEsc: false
    })

    ref.onClose.pipe(take(1)).subscribe((res) => {
      if(res) {
        this.toastr.send({
          info: true,
          message: 'Método ainda não implementado.'
        })
      }
    })
  }

  public downloadDocument() {
    this.toastr.send({
      info: true,
      message: 'Método ainda não implementado.'
    })
  }

  public contaDespesa(data): any {
    if (data.data && data.data.previsaoInicialDespesaFonte)
      return data.data.previsaoInicialDespesaFonte.codigo;
    return '';
  }

  public fonteDespesa(data): any {
    return (
      data.data.previsaoInicialDespesaFonte?.fonteRecurso &&
      `${data.data.previsaoInicialDespesaFonte?.fonteRecurso.codigo} - ${data.data.previsaoInicialDespesaFonte?.fonteRecurso.nome}`
    );
  }

  public planoDespesa(data): any {
    if (data.data && data.data.planoDespesa)
      return data.data.planoDespesa.codigo;
  }

  public fornecedor(data): any {
    if (data.data && data.data.fornecedor)
      return (
        data.data.fornecedor.pessoaCodigo +
        ' - ' +
        data.data.fornecedor.pessoaNome
      );
  }

  public getFilteredData(data: EffortInterface[]) {
    if (data) {
      this.dataSource = new DataSource({
        store: {
          data: data,
          key: 'uuid',
          type: 'array',
        },
      });
    } else {
      this.fetchGrid();
    }
  }

  public filterExpr(this: Column, valor: string, filtroSelecionado: string) {
    return [this.dataField, filtroSelecionado, parseInt(valor)];
  }

  public openModalRelatorioEmpenhoServidor() {
    this.dialogService.open(RelatorioEmpenhoServidorComponent, {
      context: {
        modal: true,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    });
  }

  public openModalRelatorioExtratoEmpenho(): void {
    this.dialogService.open(EffortStatementComponent, {
      context: {
        modal: true,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    });
  }
}
