<eqp-standard-page
  [mainTitle]="pageTitle"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonTitle]="'Atualizar'"
  [topRightButtonId]="'effortList'"
  (topRightButtonEmitter)="fetchGrid()"
>
  <eqp-loading *ngIf="loading"></eqp-loading>
  <ng-container>
    <div class="d-flex justify-content-between">
      <eqp-nebular-button
        [buttonShape]="'rectangle'"
        [buttonText]="'Empenhos em Processamento'"
        (buttonEmitter)="openEffortQueue()"
        [buttonType]="'primary'"
        [buttonIcon]="'fas fa-file-import'"
        [buttonIconVisible]="true"
        [buttonVisible]="true"
        [buttonDisabled]="false"
        [tabIndex]="1"
      ></eqp-nebular-button>
      
      <div class="d-flex mr-3" style="gap: 0.5rem;">
        <i nbTooltip="Extrato do Empenho"  class="fas fa-receipt icons-size" (click)="openModalRelatorioExtratoEmpenho()"></i>
        <i nbTooltip="Relatório Empenho x Servidor" class="fas fa-users icons-size" (click)="openModalRelatorioEmpenhoServidor()"></i>
      </div>
    </div>
    <eqp-effort-search-filter (effortData)="getFilteredData($event)" class="mt-3"></eqp-effort-search-filter>
    <dx-data-grid
      class="mt-3"
      id="effortListGrid"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [dataSource]="dataSource"
      [showColumnLines]="false"
      [showRowLines]="false"
      [dateSerializationFormat]="'yyyy-MM-dd'"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onToolbarPreparing)="onToolbarPreparing($event)"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>

      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar empenho"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="nivelPermissao === 'FULL'"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column [calculateFilterExpression]="filterExpr" alignment="left" dataField="numero" caption="Nº"></dxi-column>
      <dxi-column [allowSearch]="false" [editorOptions]="{maxLength: 10}" dataField="data" [editorOptions]="{maxLength: 10}" dataType="date" caption="Data"></dxi-column>
      <dxi-column
        alignment="left"
        dataField="previsaoInicialDespesaFonte.uuid"
        caption="Conta de despesa"
        [allowSorting]="false"
        [allowSearch]="false"
        cellTemplate="previsaoInicialDespesaFonteColumn"
      >
        <dxo-lookup
          [dataSource]="previsaoInicialDespesaFonteData"
          displayExpr="codigo"
          valueExpr="uuid"
        >
        </dxo-lookup>
      </dxi-column>
      <dxi-column
        dataField="planoDespesa.uuid"
        caption="Natureza de despesa"
        [allowSorting]="false"
        [allowSearch]="false"
        cellTemplate="planoColumn"
      >
        <dxo-lookup
          [dataSource]="planoDespesaData"
          displayExpr="codigo"
          valueExpr="uuid"
        >
        </dxo-lookup
      ></dxi-column>
      <dxi-column
        alignment="left"
        dataField="previsaoInicialDespesaFonte.uuid"
        caption="Fonte"
        [allowSorting]="false"
        [allowSearch]="false"
        [allowFiltering]="false"
        cellTemplate="fonteColumn"
      >
      </dxi-column>
      <dxi-column
        dataField="fornecedor.uuid"
        caption="Fornecedor"
        [allowSorting]="false"
        [allowSearch]="false"
        cellTemplate="fornecedorColumn"
      >
        <dxo-lookup
          [dataSource]="fornecedorData"
          displayExpr="pessoaNome"
          valueExpr="uuid"
        >
        </dxo-lookup
      ></dxi-column>

      <div *dxTemplate="let data of 'previsaoInicialDespesaFonteColumn'">
        {{ contaDespesa(data) }}
      </div>

      <div *dxTemplate="let data of 'fonteColumn'">
        {{ fonteDespesa(data) }}
      </div>

      <div *dxTemplate="let data of 'planoColumn'">
        {{ planoDespesa(data) }}
      </div>

      <div *dxTemplate="let data of 'fornecedorColumn'">
        {{ fornecedor(data) }}
      </div>

      <dxi-column
        alignment="left"
        dataField="valor"
        dataType="number"
        [format]="currencyFormat"
        caption="Valor empenhado"
        alignment="right"
      ></dxi-column>

      <dxi-column
        dataField="uuid"
        caption=""
        [width]="130"
        [allowFiltering]="false"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'" class="d-flex justify-content-center">
        <!-- <eqp-accounting-entry-link-button [numeros]="data.data?.numerosLancamentos"></eqp-accounting-entry-link-button> -->
        <a
          title="Alterar"
          (click)="edit(data.value)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          title="Imprimir"
          (click)="print(data.value)"
          class="dx-link dx-link-edit fas fa-print dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          title="Acompanhamento Empenho"
          (click)="openEffortFollow(data.data)"
          class="dx-link dx-link-edit fas fa-tasks dx-link-icon btn-icon-grid"
        >
        </a>
        <eqp-acao-assinatura-digital
          [flagDocumentoAssinado]="data.data.assinado"
          [tipoMovimento]="'EMPENHO'"
          [movimentoUuid]="data.value"
          (loading)="loading = $event"
        ></eqp-acao-assinatura-digital>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
