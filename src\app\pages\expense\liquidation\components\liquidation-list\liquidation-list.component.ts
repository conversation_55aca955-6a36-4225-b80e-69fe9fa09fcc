import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component';
import { SendDocumentComponent } from '@common/dialogs/send-document/send-document.component';
import { CrudService } from '@common/services/crud.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogService } from '@nebular/theme';
import { MenuService } from '@pages/menu.service';
import { BOOLEAN_DATA } from '@pages/shared/helpers/boolean-data';
import { currencyFormat } from '@pages/shared/helpers/format.helper';
import { DataSourceHelperService } from '@pages/shared/services/data-source-helper.service';
import DataSource from 'devextreme/data/data_source';
import { Column } from 'devextreme/ui/data_grid';
import { Subject } from 'rxjs';
import { take } from 'rxjs/operators';
import { LiquidationService } from '../../services/liquidation.service';
import { LiquidacaoBaseEfetivacaoComponent } from '../liquidacao-base-efetivacao/liquidacao-base-efetivacao.component';
import { LiquidationNoteComponent } from '../liquidation-note/liquidation-note.component';

@Component({
  selector: 'eqp-liquidation-list',
  templateUrl: './liquidation-list.component.html',
  styleUrls: ['./liquidation-list.component.scss'],
})
export class LiquidationListComponent
  extends LiquidacaoBaseEfetivacaoComponent
  implements OnInit
{
  public pageTitle = 'Liquidação';
  public dataSource: DataSource;
  public currencyFormat = currencyFormat;
  public exerciseData: any[];
  booleanData = BOOLEAN_DATA
  public loading: boolean = false
  public providerData: any
  public action: 'empty' | 'pending' | 'completed' = 'empty'
  public digitalSignature: boolean = false
  public destroy$ = new Subject<void>()

  customDisplay(item) {
    return item == 'S' ? 'Sim' : 'Não';
  }

  constructor(
    public menuService: MenuService,
    public router: Router,
    private crudService: CrudService,
    protected dialog: NbDialogService,
    private service: LiquidationService,
    protected toastr: ToastrService,
    private dataSourceHelperService: DataSourceHelperService,
  ) {
    super(dialog, toastr, service, menuService, router);
    this.permissao('/despesa/liquidacao');
  }

  ngOnInit(): void {
    this.fetchGrid();
    this.loadLookUps()
    this.efetivacaoHandlers()
  }

  efetivacaoHandlers() {
    this.getStatusEfetivacao().subscribe(res => {
      if(res === 'FINALIZADA') {
        this.dataSource.reload()
      }
    })
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  public async fetchGrid() {
    this.exerciseData =
      (
        await this.crudService
          .getSingleData<any>('exercicio', { take: 0 })
          .toPromise()
      ).data || [];


    this.dataSource = this.dataSourceHelperService.getDataSource(
      'liquidacao/reduzido',
      'uuid',
      10,
      'numero',
    );
  }

  public loadLookUps() {
    this.providerData = {
      store: this.service.getDataSourceFiltro('uuid', 'fornecedor', 10),
      pageSize: 10,
      paginate: true,
    };
  }

  public view(value) {
    this.gravarParametros()
    this.router.navigate([`despesa/liquidacao/view/${value}`]);
  }

  private new() {
    this.gravarParametros()
    this.router.navigate(['despesa/liquidacao/novo']);
  }

  public print(data) {
    this.dialog.open(LiquidationNoteComponent, {
      context: {
        liquidation: data?.data,
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    });
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever';
          item.options.text = 'Liquidação';
          item.options.hint = 'Cadastrar nova liquidação de empenho';
          item.options.onClick = () =>
            this.new()
        }
      });
    }
  }

  effect(uuid: string) {
    const ref = this.dialog.open(ConfirmationComponent, {
      context: {
        confirmationContent: {
          body: 'Deseja efetivar a liquidação?',
          confirmType: 'success',
        },
        dialogSize: 'small',
        exiberIcones: false    
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    });

    ref.onClose.pipe(take(1)).subscribe((res) => {
      if (res) {
        this.processarEfetivacao(uuid)
      }
    });
  }

  public providerDisplay(data): any {
    if (data.data && data.data.empenho.fornecedor)
      return (
        data.data.empenho.fornecedor.pessoaCodigo +
        ' - ' +
        data.data.empenho.fornecedor.pessoaNome
      );
  }

  public filterExpr(this: Column, valor: string, filtroSelecionado: string) {
    return [this.dataField,filtroSelecionado,parseInt(valor)]
  }

  public sendDocument() {
    const ref = this.dialog.open(SendDocumentComponent, {
      context: {
        dialogTitle: 'Confirma o envio do documento para assinatura?',
        dialogSize: 'medium'
      },
      closeOnBackdropClick: false,
      closeOnEsc: false
    })

    ref.onClose.pipe(take(1)).subscribe((res) => {
      if(res) {
        this.toastr.send({
          info: true,
          message: 'Método ainda não implementado.'
        })
      }
    })
  }

  public downloadDocument() {
    this.toastr.send({
      info: true,
      message: 'Método ainda não implementado.'
    })
  }

  public validarFiltroFornecedor(valor: string) {
    return [['empenho.fornecedor.uuid','contains',valor],'or',['empenhoOriginal.fornecedor.uuid','contains',valor]]
  }

  public filtrarPorExercicioEmpenho(valor: string) {
    return ["exercicioEmpenho","=",valor]
  }
}
