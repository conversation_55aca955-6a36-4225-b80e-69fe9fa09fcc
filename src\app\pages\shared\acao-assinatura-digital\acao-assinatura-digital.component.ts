import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { AcaoAssinaturaDigitalService } from './services/acao-assinatura-digital.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { filter, finalize, take, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { NbDialogService } from '@nebular/theme';
import { SendDocumentComponent } from '@common/dialogs/send-document/send-document.component';

export enum TipoMovimentoAssinaturaDigital {
  EMPENHO = 'EMPENHO',
  LIQUIDACAO = 'LIQUIDACAO',
  PAGAMENTO = 'PAGAMENTO',
}

enum StatusAssinatura {
  ASSINADO = 'S',
  NAO_ASSINADO = 'N',
}

interface ConfiguracaoMovimento {
  urlDownload: string;
  urlAssinatura: string;
  nomeArquivo: string;
}

export const CONFIGURACAO_MOVIMENTOS: Record<
  TipoMovimentoAssinaturaDigital,
  ConfiguracaoMovimento
> = {
  [TipoMovimentoAssinaturaDigital.EMPENHO]: {
    urlDownload: 'despesa_empenho/documento',
    urlAssinatura: 'despesa_empenho/assinatura-manual',
    nomeArquivo: 'Empenho_Assinado.pdf',
  },
  [TipoMovimentoAssinaturaDigital.LIQUIDACAO]: {
    urlDownload: 'liquidacao/documento',
    urlAssinatura: 'liquidacao/assinatura',
    nomeArquivo: 'Liquidacao_Assinada.pdf',
  },
  [TipoMovimentoAssinaturaDigital.PAGAMENTO]: {
    urlDownload: 'pagamento/documento',
    urlAssinatura: 'pagamento/assinatura',
    nomeArquivo: 'Liquidacao_Assinada.pdf',
  },
};

@Component({
  selector: 'eqp-acao-assinatura-digital',
  templateUrl: './acao-assinatura-digital.component.html',
  styleUrls: ['./acao-assinatura-digital.component.scss'],
})
export class AcaoAssinaturaDigitalComponent implements OnInit, OnDestroy {
  @Output() loading = new EventEmitter<boolean>(false);
  @Input() flagDocumentoAssinado: StatusAssinatura | null = null;
  flagPermiteAssinatura: 'S' | 'N' | null = null;
  @Input() tipoMovimento: TipoMovimentoAssinaturaDigital = TipoMovimentoAssinaturaDigital.EMPENHO;
  @Input() movimentoUuid: string; // Uuid do movimento, ex: Empenho, Liquidação...

  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly service: AcaoAssinaturaDigitalService,
    private readonly toastr: ToastrService,
    private readonly dialogService: NbDialogService,
    private acaoAssinaturaDigitalService: AcaoAssinaturaDigitalService,
  ) {}

  ngOnInit(): void {

    console.log(this.tipoMovimento)
    this.acaoAssinaturaDigitalService
      .verificaAssinaturaDisponivel(TipoMovimentoAssinaturaDigital[this.tipoMovimento])
      .pipe(take(1))
      .subscribe((res) => {
        this.flagPermiteAssinatura = res.dados;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  get configuracaoMovimento() {
    return CONFIGURACAO_MOVIMENTOS[this.tipoMovimento];
  }

  public carregarDocumentoAssinado() {
    const configuracao = this.configuracaoMovimento;

    this.service
      .baixarDocumento(configuracao.urlDownload, this.movimentoUuid)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) =>
          this.processarRespostaDocumento(response.dados, configuracao),
      });
  }

  private processarRespostaDocumento(
    documento: string,
    configuracao: ConfiguracaoMovimento,
  ): void {
    if (!documento) {
      this.exibirMensagemErro('Documento não encontrado');
      return;
    }

    this.baixarDocumento(documento, configuracao.nomeArquivo);
    this.exibirMensagemSucesso('Documento baixado com sucesso');
  }

  private processarAssinaturaManual() {
    const configuracao = this.configuracaoMovimento;

    this.loading.emit(true);
    this.service
      .assinaturaManual(configuracao.urlAssinatura, this.movimentoUuid)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.loading.emit(false)),
      )
      .subscribe({
        next: (response) =>
          this.exibirMensagemSucesso('Assinatura manual realizada com sucesso'),
      });
  }

  private baixarDocumento(urlDocumento: string, nomeArquivo: string): void {
    try {
      const link = document.createElement('a');
      link.href = urlDocumento;
      link.download = nomeArquivo;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      this.exibirMensagemErro('Erro ao baixar o documento');
    }
  }

  private exibirMensagemSucesso(mensagem: string): void {
    this.toastr.send({
      success: true,
      message: mensagem,
    });
  }

  private exibirMensagemErro(mensagem: string): void {
    this.toastr.send({
      error: true,
      message: mensagem,
    });
  }

  public modalConfirmarAssinaturaManual() {
    const ref = this.dialogService.open(SendDocumentComponent, {
      context: {
        dialogTitle: 'Confirma o envio do documento para assinatura?',
        dialogSize: 'medium',
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    });

    ref.onClose
      .pipe(
        take(1),
        filter((res) => res),
      )
      .subscribe((_) => {
        this.processarAssinaturaManual();
      });
  }
}
