import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { UserDataService } from '@guards/services/user-data.service';
import { DefaultInterface } from '@pages/shared/interfaces/default-interface';
import { ReportDialogService } from '@pages/shared/reports/services/report-dialog.service';
import moment from 'moment';
import { Subject, Subscription } from 'rxjs';
import { distinctUntilChanged, filter, take, takeUntil } from 'rxjs/operators';
import {
  EFFORT_OPTION_DATA,
  ORDENATION_DATA,
  PRINT_OUT_DATA,
} from './dataHelper';
import { removerPontosString } from '@common/helpers/remove-pontos-string';
import { CrudService } from '@common/services/crud.service';
import { NbDialogService } from '@nebular/theme';
import { ProjectActivitySearchComponent } from '@pages/shared/searchs/project-activity-search/project-activity-search.component';

@Component({
  selector: 'eqp-balance-executed-budget-expenditure',
  templateUrl: './balance-executed-budget-expenditure.component.html',
  styleUrls: ['./balance-executed-budget-expenditure.component.scss'],
})
export class BalanceExecutedBudgetExpenditureComponent
  implements OnInit, OnDestroy
{
  pageTitle: string = 'Relatório do Saldo da Despesa Orçamentária Executada';
  model: FormGroup;
  loading: boolean = false;

  effortOptionData: DefaultInterface[] = EFFORT_OPTION_DATA;
  printOutData: DefaultInterface[] = PRINT_OUT_DATA;
  ordenationData: DefaultInterface[] = ORDENATION_DATA;
  public fileTypeData: { chave: string; valor: string }[];
  public orgaoUuid: string;

  private unsub$ = new Subject<null>();

  exerciseFieldDisabled: boolean = true;
  effortOriginSubscription: Subscription;
  dateSubscription: Subscription;

  public baseUri = 'relatorio_gerencial_saldo_despesa_executada';

  constructor(
    private builder: FormBuilder,
    private reportDialogService: ReportDialogService,
    private userDataService: UserDataService,
    private toastr: ToastrService,
    private crudService: CrudService,
    private dialog: NbDialogService,
  ) {}

  ngOnDestroy(): void {
    this.effortOriginSubscription.unsubscribe();
    this.unsub$.next()
    this.unsub$.complete()
  }

  ngOnInit(): void {
    this.model = this.getNewModel();
    this.loadDate();
    this.loadHandlers();
    this.obterLoadingRelatorio()
  }

  private obterLoadingRelatorio() {
    this.reportDialogService
      .getLoading()
      .pipe(takeUntil(this.unsub$))
      .subscribe((res) => {
        this.loading = res;
      });
  }

  private loadDate() {
    const date = moment().format('YYYY-MM-DD');
    this.model.get('saldoEm').patchValue(date);
  }

  private loadHandlers() {
    this.effortOriginSubscription = this.model
      .get('origemEmpenho')
      .valueChanges.pipe(distinctUntilChanged(), takeUntil(this.unsub$))
      .subscribe((value) => {
        if (value == 2 || value == 3) this.exerciseFieldDisabled = false;
        else this.exerciseFieldDisabled = true;
      });

    this.model
      .get('orgao')
      .valueChanges.pipe(takeUntil(this.unsub$))
      .subscribe((uuid: string) => {
        if (!uuid) this.model.get('unidade').reset();
        this.orgaoUuid = uuid;
      });

    const loggedExercise = parseInt(this.userDataService.userData.exercicio);
    this.dateSubscription = this.model
      .get('saldoEm')
      .valueChanges.pipe(distinctUntilChanged(), takeUntil(this.unsub$))
      .subscribe((value) => {
        if (
          moment(value).year() > loggedExercise ||
          moment(value).year() < loggedExercise
        ) {
          this.invalidDateMessage();
          this.model.get('saldoEm').reset();
          this.model.get('saldoEm').markAsPristine();
        }
      });
  }

  private invalidDateMessage() {
    this.toastr.send({
      error: true,
      message:
        'O campor "Saldo em" só permite datas dentro do exercício logado.',
    });
  }

  getNewModel() {
    return (this.model = this.builder.group({
      saldoEm: [Validators.required],
      origemEmpenho: [1, Validators.required],
      exercicio: [],
      tipoImpressaoEmpenho: [1, Validators.required],
      detalharFlag: ['N'],
      tipoOrdenacao: [1, Validators.required],
      fornecedorUuid: [],
      orgao: [],
      unidade: [],
      funcao: [],
      subfuncao: [],
      programa: [],
      projetoAtividade: [],
      fonteRecurso: [],
      naturezaDespesa: [],
    }));
  }

  buscarProjetoAtividadePorTipoOrdem(tipoOrdem: string) {
    if(!tipoOrdem || tipoOrdem === '') {
      this.model.get('projetoAtividade').reset();
      return
    }

    const tipo = parseInt(tipoOrdem.substring(0, 1))
    const ordem = parseInt(tipoOrdem.substring(1))

    console.log(tipo, ordem)
    this.crudService
      .getDataSourceFiltroComposto(
        'uuid', 
        `${this.baseUri}/projeto_atividade`, 
        0, 
        `["tipo","=",${tipo}],"and",["ordem","=",${ordem}]`
      )
      .load()
      .then((res) => {
        if (res.length === 0) {
          this.toastr.send({
            error: true,
            message: 'Projeto/Atividade não encontrado.',
          });
          this.model.get('projetoAtividade').reset();
          return;
        }
        this.model.get('projetoAtividade').patchValue(res[0]);
      });
  }

  buscarProjetoAtividadeModal() {
    this.dialog.open(
      ProjectActivitySearchComponent, {
        context: {},
        closeOnEsc: false,
        closeOnBackdropClick: false,
      }
    ).onClose
      .pipe(take(1), filter((res) => res))
      .subscribe((res) => {
        this.model.get('projetoAtividade').patchValue(res);
      });
  }

  public prepararDto(formData: any) {
    const dto = {
      ...formData,
      projetoAtividadeTipo: formData.projetoAtividade?.tipo ? parseInt(formData.projetoAtividade?.tipo) : null,
      projetoAtividadeOrdem: formData.projetoAtividade?.ordem ? parseInt(formData.projetoAtividade?.ordem) : null,
      orgaoCodigo: formData.orgao?.codigo ? parseInt(formData.orgao.codigo) : null,
      unidadeCodigo: formData.unidade?.codigo ? parseInt(formData.unidade?.codigo) : null,
      funcaoCodigo: formData.funcao?.codigo ? parseInt(formData.funcao.codigo) : null,
      subfuncaoCodigo: formData.subfuncao?.codigo ? parseInt(formData.subfuncao.codigo) : null,
      programaCodigo: formData.programa?.codigo ? parseInt(formData.programa.codigo) : null,
      fonteRecursoCodigo: formData.fonteRecurso?.codigo ? parseInt(formData.fonteRecurso.codigo) : null,
      naturezaDespesaCodigo: formData.naturezaDespesa?.codigo ? removerPontosString(formData.naturezaDespesa?.codigo) : null
    }

    delete dto.orgao;
    delete dto.unidade;
    delete dto.funcao;
    delete dto.subfuncao;
    delete dto.programa;
    delete dto.fonteRecurso;
    delete dto.naturezaDespesa;
    delete dto.projetoAtividade;

    return dto;
  }
}
