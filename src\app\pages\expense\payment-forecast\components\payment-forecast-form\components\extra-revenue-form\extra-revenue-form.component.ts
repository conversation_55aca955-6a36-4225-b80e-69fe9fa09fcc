import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { CrudService } from '@common/services/crud.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef, NbDialogService } from '@nebular/theme';
import { AccountingPlanExtraSearchDialogComponent } from '@pages/shared/searchs/accounting-plan-extra-search-dialog/accounting-plan-extra-search-dialog.component';
import { Observable, Subject} from 'rxjs';
import { filter, finalize, first, take, takeUntil } from 'rxjs/operators';
import { PaymentExtraRevenueService } from './../../../../services/payment-extra-revenue.service';
import { BankTransferSearchDialogComponent } from '@pages/shared/searchs/bank-transfer-search-dialog/bank-transfer-search-dialog.component';
import { LogInterface } from '@pages/shared/interfaces/log';
import { AccountPlanExtraInterface } from '@pages/extra-budget/configurations/interfaces/account-plan-extra';
import { EffortInterface } from '@pages/expense/effort/interfaces/effort';
import { AccountingPlanExtraInterface } from '@pages/shared/interfaces/accounting-plan-extra';
import { PaymentExtraRevenueInterface } from '@pages/expense/payment-forecast/interfaces/payment-extra-revenue';
import { EfdReinfConfigurationInterface } from '@pages/shared/interfaces/efd-reinf-configuration';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { MenuService } from '@pages/menu.service';
import { Router } from '@angular/router';
import { PaymentService } from '@pages/expense/payment-forecast/services/payment.service';

@Component({
  selector: 'eqp-extra-revenue-form',
  templateUrl: './extra-revenue-form.component.html',
  styleUrls: ['./extra-revenue-form.component.scss'],
})
export class ExtraRevenueFormComponent extends BaseTelasComponent implements OnInit, OnDestroy {
  pageTitle: string = 'Retenção extra';
  loading: boolean = false;

  @Input() parentUuid: string;
  @Input() uuid: string;
  @Input() isEffected: boolean = true;
  @Input() empenho: EffortInterface;
  public calculationBaseRequired: boolean = false
  public logData: LogInterface
  public permissaoCadastro: boolean
  flagInformacoesEfdReinf
  public model: FormGroup;
  public permissaoTela: boolean
  private destroy$ = new Subject()

  constructor(
    private dialogService: NbDialogService,
    private crudService: CrudService,
    private dialogRef: NbDialogRef<ExtraRevenueFormComponent>,
    private builder: FormBuilder,
    private service: PaymentExtraRevenueService,
    private toastr: ToastrService,
    public menuService: MenuService,
    public router: Router,
    private paymentForecastService: PaymentService,
  ) {
    super(menuService, router);
  }

  set setFlagInformacoesEfdReinf(valor: string) {
    this.flagInformacoesEfdReinf = valor
  }

  ngOnInit(): void {
    this.obterPermissao()
    this.model = this.getNewModel();
    if (this.uuid) 
      this.loadPageData(this.uuid);
    else
      this.carregarDadosConfiguracaoEfd(this.empenho)
  }

  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }
  
  obterPermissao() {
    this.paymentForecastService
      .getPermissao()
      .pipe(takeUntil(this.destroy$))
      .subscribe((res) => {
        this.permissaoTela = res === 'FULL' || res === 'EDITOR';
      });
  }
  
  private async carregarDadosConfiguracaoEfd(empenho: EffortInterface) {
    if(!empenho)
      return
    this.model.get('naturezaDespesaEmpenho').patchValue(empenho.planoDespesa.codigo)
    const {planoDespesa: {uuid}} = empenho
    const informacoesEfd: EfdReinfConfigurationInterface[] = await this.crudService.getDataSourceFiltro('uuid', 'configuracao_efd_reinf', 0, "naturezaDespesa.uuid", uuid).load()
    if(informacoesEfd.length)
      this.obterInformacoesEmpenho(empenho, informacoesEfd[0])
  }

  private obterInformacoesEmpenho(empenho: EffortInterface, informacoesEfd: EfdReinfConfigurationInterface) {
    let dto = informacoesEfd
    const naturezaRendimentoMapping: {[tipoPessoaId: number]: number} = {
      1: dto.naturezaRendimentoPf,
      2: dto.naturezaRendimentoPj,
      3: dto.naturezaRendimentoPf,
      4: dto.naturezaRendimentoPj,
    };
    const {tipoPessoaId} = empenho.fornecedor
    const naturezaRendimento = naturezaRendimentoMapping[tipoPessoaId] || null;
    const codigoNaturezaRendimento = this.model.get('codigoNaturezaRendimento').value
    if(!codigoNaturezaRendimento) 
      this.model.get('codigoNaturezaRendimento').patchValue(naturezaRendimento)
  }

  public get planoContabilExtra() {
    return this.model.get('planoContabilExtra').value
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      planoContabilExtra: [undefined, [Validators.required]],
      fonteRecurso: [undefined, [Validators.required]],
      fornecedor: [undefined, [Validators.required]],
      valor: [undefined, [Validators.required]],
      baseCalculo: [undefined, [Validators.required]],
      transferenciaBancaria: [undefined],
      flagInformacoesEfdReinf: ['N'],
      codigoNaturezaRendimento: [],
      fornecedorSubcontratada: [],
      naturezaDespesaEmpenho: [],
    }, {
      validator: (fg: FormGroup) => {
        const baseCalculo = +fg.get('baseCalculo').value

        if(baseCalculo <= 0 && this.calculationBaseRequired)
          fg.get('baseCalculo').setErrors({customError: 'A base de cálculo deve ser maior do que 0.'})
        else
          fg.get('baseCalculo').setErrors(null)
      }
    });
  }

  loadPageData(uuid: string) {
    this.loading = true;
    this.service
      .getIndividual(this.parentUuid, uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe((res) => {
        this.loadForm(res.dados);
      });
  }

  loadForm(data: PaymentExtraRevenueInterface) {
    this.logData = data.log
    if(data.fornecedorSubcontratada || data.codigoNaturezaRendimento || data.baseCalculo)
      this.setFlagInformacoesEfdReinf = 'S'
    this.calculationBaseRequired = data.planoContabilExtra?.tipoTributoEfdUuid ? true : false
    const dto = {
      ...data,
      planoContabilExtra: {
        uuid: data.planoContabilExtra?.uuid,
        codigo: data.planoContabilExtra?.planoContabil?.codigoReduzido,
        nome: data.planoContabilExtra?.planoContabil?.codigo + ' - ' + data.planoContabilExtra?.planoContabil?.nome,
      },
      fornecedor: {
        uuid: data.fornecedor?.uuid,
        pessoaNome: data.fornecedor?.pessoaNome,
        pessoaCodigo: data.fornecedor?.pessoaCodigo,
      },
      transferenciaBancaria: {
        uuid: data.transferenciaBancaria?.uuid,
        nome: data.transferenciaBancaria?.descricao,
        codigo: data.transferenciaBancaria?.numero,
      },
    };
    this.model.patchValue(dto);
    this.carregarDadosConfiguracaoEfd(this.empenho)
  }

  cancel() {
    this.dialogRef.close();
  }

  getObjectsUuid(formData: any) {
    for (let atributo in formData) {
      if (
        formData[atributo] &&
        typeof formData[atributo] === 'object' &&
        formData[atributo].uuid
      ) {
        formData[atributo] = {
          uuid: formData[atributo].uuid,
        };
      }
    }
    return formData;
  }

  prepare(formData: any) {
    if(formData.flagInformacoesEfdReinf === 'N') {
      delete formData.codigoNaturezaRendimento
      delete formData.fornecedorSubcontratada
      delete formData.baseCalculo
    }
    const dto = this.getObjectsUuid(formData);
    dto.baseCalculo = formData.baseCalculo || 0
    
    return dto;
  }

  submit() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue());
      let req: Observable<any>;
      if (this.uuid) {
        req = this.service.put(this.parentUuid, dto, this.uuid);
      } else {
        req = this.service.post(this.parentUuid, dto);
      }
      this.loading = true;
      req
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe((res) => {
          this.toastr.send({
            title: 'Sucesso',
            message: `Retenção extra ${
              this.uuid ? 'atualizada' : 'cadastrada'
            } com sucesso.`,
            success: true,
          });
          this.dialogRef.close(res.body.dados);
        });
    }
  }

  private efdTypeTributeRules(data: AccountPlanExtraInterface) {
    this.calculationBaseRequired = data.tipoTributoEfdUuid ? true : false
    data.tipoTributoEfdUuid
      ? this.setFlagInformacoesEfdReinf = 'S'
      : this.setFlagInformacoesEfdReinf = 'N'
  }

  onAccountingPlanExtraDialog() {
    const dialogRef = this.dialogService.open(
      AccountingPlanExtraSearchDialogComponent,
      {},
    );
    dialogRef.componentRef.instance.uri = 'pagamento/plano_contabil_extra';
    dialogRef.onClose.pipe(filter((res) => res)).subscribe((res) => {
      this.efdTypeTributeRules(res)
      this.carregarDadosPlanoContabilExtra(res)
      this.model.get('planoContabilExtra').markAsDirty();
    });
  }

  public async onAccountingPlanInput(codigoReduzido: string) {
    if (!codigoReduzido || codigoReduzido == '') {
      this.model.get('planoContabilExtra').reset()
      return;
    }
    this.loading = true;
    const planoContabilExtra: AccountingPlanExtraInterface[] = 
      await this.crudService.getDataSourceFiltro(
        'uuid',
        'pagamento/plano_contabil_extra',
        1,
        'codigoReduzido',
        codigoReduzido
      ).load();
    if (planoContabilExtra.length == 0) {
      this.toastr.send({
        error: true,
        message: 'Conta contábil não encontrada',
      });
      this.model.get('planoContabilExtra').reset()
    } else {
      this.carregarDadosPlanoContabilExtra(planoContabilExtra[0]);
    }
    this.loading = false;
  }

  private carregarDadosPlanoContabilExtra(planoContabilExtra: AccountingPlanExtraInterface) {
    const {planoContabil} = planoContabilExtra
    this.model.get('planoContabilExtra').patchValue({
      uuid: planoContabilExtra.uuid,
      codigo: planoContabil.codigoReduzido,
      nome: `${planoContabil.codigo} - ${planoContabil.nome}`
    })
  }

  onTransferBankDialog() {
    const ref = this.dialogService.open(BankTransferSearchDialogComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    });
    ref.componentRef.instance.uri = 'pagamento/transferencia_banco';
    ref.onClose
      .pipe(
        filter((res) => res),
        first(),
      )
      .subscribe((res) => {
        if (res) {
          const provider = {
            uuid: res.uuid,
            codigo: res.numero,
            nome: res.descricao,
          };
          this.model.get('transferenciaBancaria').patchValue(provider);
          this.model.get('transferenciaBancaria').markAsDirty();
        }
      });
  }

  onTransferBankInput(value: string) {
    if (!value || value == '' || !(/^\d+$/.test(value))) {
      this.model.get('transferenciaBancaria').reset();
      return
    }
    this.loading = true;
    this.crudService
      .getDataSourceFiltro(
        'uuid',
        'pagamento/transferencia_banco',
        10,
        'numero',
        `${value}`,
      )
      .load()
      .then(
        (res) => {
          this.loading = false;
          if (res.length == 0) {
            this.toastr.send({
              error: true,
              message: 'Transferência bancária não encontrada.',
            });
            this.model.get('transferenciaBancaria').reset();
          } else {
            const provider = {
              uuid: res[0].uuid,
              codigo: res[0].numero,
              nome: res[0].descricao,
            };
            this.model.get('transferenciaBancaria').patchValue(provider);
          }
        },
        (err) => {
          this.loading = false;
          this.toastr.bulkSend({
            error: true,
            message: `${err}`,
          });
        },
      );
  }
}
