<eqp-nebular-dialog
  [dialogTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  [rightFirstButtonVisible]="permissaoTela"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonId]="'submit-payment-extra-revenue'"
  [rightFirstButtonDisabled]="model.pristine || model.invalid"
  (rightFirstButtonEmitter)="submit()"
>
  <ng-container [formGroup]="model">
    <div class="mb-3">
      <eqp-nebular-button
        [buttonShape]="'rectangle'"
        [buttonText]="'Buscar Retenção Extra'"
        (buttonEmitter)="buscarRetencaoExtraLiquidacao()"
        [buttonType]="'primary'"
        [buttonIcon]="'fas fa-search'"
        [buttonIconVisible]="true"
        [buttonVisible]="true"
        [buttonDisabled]="isEffected"
      ></eqp-nebular-button>
	</div>
    <div class="row mb-3">
      <div class="col-12 col-md-6">
        <eqp-nebular-search-field
          [disabled]="isEffected"
          (onButtonClick)="onAccountingPlanExtraDialog()"
          (onInputChange)="onAccountingPlanInput($event)"
          label="Contábil *"
          waitingTime="2000"
          formControlName="planoContabilExtra"
          codeLabel="Cód. Reduzido"
          nameLabel="Código - Nome"
          [onlyNumberKey]="true"
        ></eqp-nebular-search-field>
      </div>
      <div class="col-12 col-md-6">
        <eqp-search-field
          [disabled]="isEffected"
          label="Fonte de recurso *"
          formControlName="fonteRecurso"
          uri="pagamento/fonte_recurso"
          dialogTitle="Fonte recurso"
          [onlyNumberKey]="true"
          waitingTime="2000"
          [returnAllData]="true"
          searchColumnsType="resourceSourceColumns"
          [filter]="['flagRetencao','=','S']"
          messageNotFound="Fonte de recurso não encontrada."
        ></eqp-search-field>
      </div>
    </div>
    <div class="row mb-3">
      <div class="col-12 col-md-6">
        <eqp-search-field
          [disabled]="isEffected"
          label="Credor/Devedor *"
          formControlName="fornecedor"
          codeKey="pessoaCodigo"
          nameKey="pessoaNome"
          uri="pagamento/fornecedor"
          dialogTitle="Fornecedor"
          [onlyNumberKey]="true"
          waitingTime="2000"
          [returnAllData]="true"
          searchColumnsType="providerColumns"
          messageNotFound="Fornecedor não encontrado."
        ></eqp-search-field>
      </div>
      <div class="col-12 col-md-2">
        <eqp-nebular-search-field
          [disabled]="isEffected"
          label="Transferência bancária"
          (onButtonClick)="onTransferBankDialog()"
          (onInputChange)="onTransferBankInput($event)"
          [hideName]="true"
          formControlName="transferenciaBancaria"
          [onlyNumberKey]="true"
        ></eqp-nebular-search-field>
      </div>
      <div class="col-12 col-md-2">
        <eqp-nebular-input
          [disabled]="isEffected"
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Valor *"
          formControlName="valor"
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
        ></eqp-nebular-input>
      </div>
    </div>
    <div class="row">
      <div class="col col-12">
        <eqp-field-toggle
          label="Informações EFD Reinf"
          formControlName="flagInformacoesEfdReinf"
          [(ngModel)]="flagInformacoesEfdReinf"
        >
        </eqp-field-toggle>
      </div>
    </div>
    <div class="row" *ngIf="flagInformacoesEfdReinf === 'S'">
      <div class="col-12 col-md-2">
        <eqp-nebular-input
          [required]="calculationBaseRequired"
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Base de cálculo"
          formControlName="baseCalculo"
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
        ></eqp-nebular-input>
      </div>
      <div class="col-12 col-md-3">
        <eqp-nebular-input [style]="'basic'" [size]="'small'" [shape]="'rectangle'" readonly="true"
          label="Natureza despesa do empenho" formControlName="naturezaDespesaEmpenho" [style]="'basic'"
        ></eqp-nebular-input>
      </div>
      <div class="col-12 col-md-3">
        <label class="label">
          Natureza de Rendimento
          <nb-icon
            nbTooltip="Necessário informar a natureza de rendimento somente se deseja declarar com natureza de rendimento diferente da existente na configuração."
            nbTooltipStatus="warning"
            icon="question-mark-circle-outline"
            pack="eva"
            status="warning"
            [options]="{ animation: { type: 'pulse', infinite: true } }"
          ></nb-icon>
        </label>
        <input
          [type]="'number'"
          [fullWidth]="true"
          [fieldSize]="'small'"
          formControlName="codigoNaturezaRendimento"
          nbInput
        />
      </div>
    </div>
    <div class="row mt-2" *ngIf="flagInformacoesEfdReinf === 'S'">
      <div class="col col-12 col-md-6">
        <eqp-search-field
          label="Subcontrada"
          formControlName="fornecedorSubcontratada"
          [displayRequired]="true"
          uri="pagamento/fornecedor"
          searchColumnsType="providerColumns"
          dialogTitle="Fornecedor"
          codeKey="pessoaCodigo"
          nameKey="pessoaNome"
          waitingTime="2000"
          [onlyNumberKey]="true"
        >
        </eqp-search-field>
      </div>
    </div>
    <eqp-user-log [logData]="logData" *ngIf="uuid && logData"></eqp-user-log>
  </ng-container>
</eqp-nebular-dialog>
