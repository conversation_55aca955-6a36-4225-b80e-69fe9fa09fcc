import { AccountingPlanInterface } from "@pages/divided-debit/interfaces/accounting-plan"
import { GfipClassificationInterface } from "./gfip-classification"
import { RetentionTypeInterface } from "./retention-type"
import { LogInterface } from "@pages/shared/interfaces/log"

export interface AccountPlanExtraInterface {
	uuid: string,
  planoContabil: AccountingPlanInterface
  ehConsignacao: string
  permitirSemSaldo: string
  classificacaoGfip: GfipClassificationInterface,
  tipoRetencao: RetentionTypeInterface
  tipoTributoEfdUuid?: string
  log?: LogInterface
}
