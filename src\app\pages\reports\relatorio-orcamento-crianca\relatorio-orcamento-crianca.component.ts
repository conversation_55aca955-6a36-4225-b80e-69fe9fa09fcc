import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';
import { CrudService } from '@common/services/crud.service';
import { Subject } from 'rxjs';
import { take } from 'rxjs/operators';
import { NbDialogService } from '@nebular/theme';
import { FONTE_RECURSO } from './helpers/fonteRecurso';
import { FUNCAO_SUBFUNCAO } from './helpers/funcaoSubfuncao';
import { PROJETO_ATIVIDADE } from './helpers/projetoAtividade';
import { NATUREZA_DESPESA } from './helpers/naturezaDespesa';
import { ReportPreviewComponent } from '@pages/shared/reports/report-preview/report-preview.component';
import { ReportService } from '@pages/shared/reports/services/report.service';
import * as moment from 'moment';

@Component({
  selector: 'eqp-relatorio-orcamento-crianca',
  templateUrl: './relatorio-orcamento-crianca.component.html',
  styleUrls: ['./relatorio-orcamento-crianca.component.scss']
})
export class RelatorioOrcamentoCriancaComponent implements OnInit, OnDestroy {
  public model: FormGroup;
  public tituloPagina = 'Relatório Orçamento Criança';
  public loading = false;
  private unsub$ = new Subject<null>();
  private uri = 'orcamento_crianca';
  public reportTypeList: any[] = [];
  public downloadType = new FormControl('PDF');
  entidades = []
  public tiposOrdenacao = [
    { chave: 'fonteRecurso', valor: 'Fonte de recurso' },
    { chave: 'funcaoSubfuncao', valor: 'Função/Subfunção' },
    { chave: 'projetoAtividade', valor: 'Projeto/Atividade' },
    { chave: 'naturezaDespesa', valor: 'Natureza de despesa' },
  ];

  constructor(
    private builder: FormBuilder,
    private crudService: CrudService,
    private dialogService: NbDialogService,
    private reportService: ReportService
  ) { }

  ngOnInit(): void {
    this.crudService
    .getSingleData<any[]>(`entidade/paginado`)
    .pipe(take(1))
    .subscribe((res) => {
      this.entidades = res.data;
    });
    this.model = this.builder.group({
      dataInicial: ['', Validators.required],
      dataFinal: ['', Validators.required],
      ordenacao: [''],
      entidadesUuid: [[], Validators.required],
    });
    this.getReportTypes();
  }

  private getReportTypes(): void {
    this.crudService
      .getSingleData<any>(
        'transparencia/pre_carregamento_relatorio/tipo_relatorio',
      )
      .pipe(take(1))
      .subscribe(res => {
        this.reportTypeList = res.dados || [];
      });
  }

  ngOnDestroy(): void {
    this.unsub$.next();
    this.unsub$.complete();
  }

  private isAnoCompleto(): boolean {
    const dataInicial = new Date(this.model.get('dataInicial').value);
    const dataFinal = new Date(this.model.get('dataFinal').value);

    const inicioAno =
      moment(dataInicial).add(1, 'day').format('MM-DD') === '01-01';
    const fimAno = moment(dataFinal).add(1, 'day').format('MM-DD') === '12-31';
    const mesmoAno =
      dataInicial.getUTCFullYear() === dataFinal.getUTCFullYear();
    return inicioAno && fimAno && mesmoAno;
  }

  public submit(): void {
    if (!this.isAnoCompleto()) return;
    this.loading = true;
    const dto = this.prepare(this.model.getRawValue());

    // Simulando processamento
    setTimeout(() => {
      this.loading = false;

      console.log('Dados do formulário:', dto);
      console.log('Tipo de arquivo selecionado:', this.downloadType.value);

      if (this.downloadType.value === 'PDF') {

        // Abrindo o ReportPreviewComponent com dados simulados
        this.dialogService.open(ReportPreviewComponent, {
          context: {
            downloadName: this.tituloPagina,
            rInfo: {
              dto: dto,
              url: this.uri,
            },
            reportData: {
              tipo: {
                extension: 'PDF'
              },
              documento: this.getBase64ForReport(dto.ordenacao),
            },
          },
          closeOnBackdropClick: false,
          closeOnEsc: false,
        });
      } else {
        console.log('Iniciando download direto do arquivo');
        this.downloadReport(dto);
      }
    }, 800); // Simulando um pequeno delay para mostrar o loading
  }

  public downloadReport(data: any): void {
    if (!this.isAnoCompleto()) return;
    this.loading = true;

    // Simulando uma requisição com um timeout
    setTimeout(() => {
      // Criando dados fake para simular a resposta da API
      const reportData = {
        documento: this.getBase64ForReport(data.ordenacao),
        tipo: {
          extension: this.downloadType.value || 'PDF',
        },
      };

      console.log('Simulando download com os dados:', {
        reportData: reportData,
        downloadName: this.tituloPagina,
        tipoArquivo: this.downloadType.value
      });

      // Chamando o serviço de download com os dados simulados
      this.reportService.donwloadReport(
        reportData,
        this.tituloPagina,
      );

      // Desativando o loading após o "processamento"
      this.loading = false;
    }, 1500); // Simulando um delay de 1.5 segundos para parecer uma requisição real
  }

  private prepare(data: any): any {
    return { ...data };
  }

  private getBase64ForReport(ordenacao: string): string {
    switch (ordenacao) {
      case 'fonteRecurso':
        return FONTE_RECURSO;
      case 'funcaoSubfuncao':
        return FUNCAO_SUBFUNCAO;
      case 'projetoAtividade':
        return PROJETO_ATIVIDADE;
      case 'naturezaDespesa':
        return NATUREZA_DESPESA;
      default:
        return FONTE_RECURSO;
    }
  }

}


