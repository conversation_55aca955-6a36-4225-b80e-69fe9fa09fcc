import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResponseDto } from '@pages/shared/interfaces/response-dto';
import { TipoMovimentoAssinaturaDigital } from '../acao-assinatura-digital.component';

interface AssinaturaMovimentoDisponivel {
  urlVerificaAssinaturaDisponivel: string;
}

const CONFIGURACAO_MOVIMENTOS: Record<
  TipoMovimentoAssinaturaDigital,
  AssinaturaMovimentoDisponivel
> = {
  [TipoMovimentoAssinaturaDigital.EMPENHO]: {
    urlVerificaAssinaturaDisponivel: 'despesa_empenho/assinatura-disponivel',
  },
  [TipoMovimentoAssinaturaDigital.LIQUIDACAO]: {
    urlVerificaAssinaturaDisponivel: 'liquidacao/assinatura-disponivel',
  },
  [TipoMovimentoAssinaturaDigital.PAGAMENTO]: {
    urlVerificaAssinaturaDisponivel: 'pagamento/assinatura-disponivel',
  },
};

@Injectable({
  providedIn: 'root',
})
export class AcaoAssinaturaDigitalService {
  constructor(
    protected httpClient: HttpClient,
  ) {}

  public baixarDocumento(urlDownloadDocumento: string, movimentoUuid: string) {
    return this.httpClient.get<ResponseDto<string>>(
      `${urlDownloadDocumento}/${movimentoUuid}`,
    );
  }

  public assinaturaManual(urlAssinaturaManual: string, movimentoUuid: string) {
    return this.httpClient.put<ResponseDto<string>>(
      `${urlAssinaturaManual}/${movimentoUuid}`,
      {},
    );
  }

  public verificaAssinaturaDisponivel(
    tipoMovimento: TipoMovimentoAssinaturaDigital,
  ) {
    const configuracao = CONFIGURACAO_MOVIMENTOS[tipoMovimento];
    return this.httpClient.get<ResponseDto<'S' | 'N'>>(
      configuracao.urlVerificaAssinaturaDisponivel,
    );
  }
}
