import { AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { CrudService } from '@common/services/crud.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogService } from '@nebular/theme';
import { PaymentExtraRevenueService } from '@pages/expense/payment-forecast/services/payment-extra-revenue.service';
import { PaymentService } from '@pages/expense/payment-forecast/services/payment.service';
import { MenuService } from '@pages/menu.service';
import { currencyFormat } from '@pages/shared/helpers/format.helper';
import DataSource from 'devextreme/data/data_source';
import { finalize, take, takeUntil } from 'rxjs/operators';
import { ExtraRevenueFormComponent } from '../extra-revenue-form/extra-revenue-form.component';
import { FormBuilder, FormGroup } from '@angular/forms';
import { EffortInterface } from '@pages/expense/effort/interfaces/effort';
import { Subject } from 'rxjs';
import { LiquidationInterface } from '@pages/expense/liquidation/interfaces/liquidation';

@Component({
  selector: 'eqp-extra-revenue',
  templateUrl: './extra-revenue.component.html',
  styleUrls: ['./extra-revenue.component.scss'],
})
export class ExtraRevenueComponent
  extends BaseTelasComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  pageTitle = 'Retenção extra';
  parentUuid: string;
  public loading = false;
  public dataSource: DataSource;
  isEffected: boolean = true;

  public currencyFormat = currencyFormat;
  public model: FormGroup;
  public empenho: EffortInterface
  public liquidacao: LiquidationInterface
  public valorTotalRetencaoExtra: number = 0;
  public permissaoTela: boolean
  private destroy$ = new Subject()

  private formatoCurrencyBr = new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  });

  constructor(
    private dialogService: NbDialogService,
    public menuService: MenuService,
    public router: Router,
    private crudService: CrudService,
    private service: PaymentExtraRevenueService,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private paymentForecastService: PaymentService,
    private builder: FormBuilder
  ) {
    super(menuService, router);
  }

  ngAfterViewInit(): void {
    this.carregarTotalRetencoes();
  }

  ngOnInit(): void {
    this.obterPermissao()
    const { uuid } = this.route.snapshot.params;
    this.parentUuid = uuid;
    this.model = this.getNewModel()
    this.obterDadosPrevisaoPagamento(uuid);
  }

  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  obterPermissao() {
    this.paymentForecastService
      .getPermissao()
      .pipe(takeUntil(this.destroy$))
      .subscribe((res) => {
        this.permissaoTela = res === 'FULL';
      });
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      pagamento: this.builder.group({
        numeroPrevisao: [],
        data: [],
        valor: [],
        liquidacao: this.builder.group({
          numero: [],
          data: [],
          valor: [],
          empenho: this.builder.group({
            numero: [],
            anoCompetencia: [],
            data: [],
            valor: [],
            tipoEmpenho: [],
            planoDespesa: [],
          }),
        }),
      }),
      logCreate: [null],
      logUpdate: [null],
    });
  }

  private carregarTotalRetencoes() {
    this.crudService
      .getSingleObject<number>(
        `pagamento/${this.parentUuid}/receita_extra/valor_total`,
      )
      .pipe(take(1))
      .subscribe((res) => {
        this.valorTotalRetencaoExtra = res.dados;
      });
  }

  private obterDadosPrevisaoPagamento(uuid: string) {
    this.loading = true;
    this.paymentForecastService
      .getOne(uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe((paymentForecast) => {
        this.isEffected = paymentForecast.efetivado == 'S';
        this.model.get('pagamento').patchValue(paymentForecast);
        this.empenho = paymentForecast.liquidacao.empenho   
        this.liquidacao = paymentForecast.liquidacao     
      });

    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `pagamento/${this.parentUuid}/receita_extra`,
        10,
      ),
      paginate: true,
      pageSize: 10,
    });
  }

  private carregarGrid() {
    this.carregarTotalRetencoes();
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `pagamento/${this.parentUuid}/receita_extra`,
        10,
      ),
      paginate: true,
      pageSize: 10,
    });
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever';
          item.options.text = 'Retenção extra';
          item.options.hint = 'Cadastrar nova Retenção extra';
          item.options.onClick = () => this.openEditDialogByUuid();
        }
      });
    }
  }

  private openEditDialogByUuid(uuid?: string) {
    const dialogRef = this.dialogService.open(ExtraRevenueFormComponent, {
      context: {},
      closeOnBackdropClick: false,
      closeOnEsc: false,
    });
    dialogRef.componentRef.instance.parentUuid = this.parentUuid;
    dialogRef.componentRef.instance.uuid = uuid;
    dialogRef.componentRef.instance.isEffected = this.isEffected;
    dialogRef.componentRef.instance.empenho = this.empenho;
    dialogRef.componentRef.instance.liquidacao = this.liquidacao;
    dialogRef.onClose.subscribe(() => {
      this.carregarGrid();
    });
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    });

    dialogRef.onClose.subscribe((res) => {
      if (res === 'S') {
        this.service
          .delete(this.parentUuid, uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            (_) => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Retenção extra excluída com sucesso.',
              });
              this.carregarGrid();
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          );
      }
    });
  }

  public customizarTotalizador = () => {
    return `Total: ${this.formatoCurrencyBr.format(this.valorTotalRetencaoExtra)}`
  };
}
