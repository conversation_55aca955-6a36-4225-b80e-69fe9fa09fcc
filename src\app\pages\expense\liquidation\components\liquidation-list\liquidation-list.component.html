<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerStatus]="'info'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonTitle]="'Atualizar'"
  [topRightButtonId]="'liquidationList'"
  (topRightButtonEmitter)="fetchGrid()"
  [spinnerActive]="loading"
>
  <ng-container>
    <dx-data-grid
      class="mt-3"
      id="liquidationListGrid"
      [dateSerializationFormat]="'yyyy-MM-dd'"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [dataSource]="dataSource"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onToolbarPreparing)="onToolbarPreparing($event)"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>

      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="nivelPermissao === 'FULL'"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column [calculateFilterExpression]="filterExpr" alignment="left" dataField="numero" caption="Nº"></dxi-column>
      <dxi-column
        dataType="date"
        alignment="left"
        dataField="data"
        [editorOptions]="{maxLength: 10}"
        caption="Data"
        [allowSearch]="false"
      ></dxi-column>
      <dxi-column
        dataField="empenho.fornecedor.uuid"
        caption="Fornecedor"
        [allowSorting]="false"
        [allowSearch]="false"
        [calculateFilterExpression]="validarFiltroFornecedor"
        cellTemplate="providerColumn"
        [width]="300"
      >
        <dxo-lookup
          [dataSource]="providerData"
          displayExpr="pessoaNome"
          valueExpr="uuid"
        >
        </dxo-lookup
      ></dxi-column>
      <div *dxTemplate="let data of 'providerColumn'">
        {{ providerDisplay(data) }}
      </div>
      <dxi-column
        alignment="left"
        dataField="empenho.numero"
        [calculateFilterExpression]="filterExpr"
        caption="Nº Empenho"
      ></dxi-column>
      <dxi-column
        alignment="left"
        caption="Exercício"
        dataField="empenho.anoCompetencia"
        [calculateFilterExpression]="filtrarPorExercicioEmpenho"
      >
        <dxo-lookup
          [dataSource]="exerciseData"
          displayExpr="exercicio"
          valueExpr="exercicio"
        >
        </dxo-lookup>
      </dxi-column>

      <dxi-column
        [allowEditing]="false"
        alignment="left"
        [editorOptions]="{maxLength: 10}"
        dataType="date"
        dataField="empenho.data"
        caption="Data Empenho"
      ></dxi-column>

      <dxi-column
        alignment="left"
        dataField="empenho.tipoEmpenho.nome"
        caption="Tipo"
      ></dxi-column>

      <dxi-column
        alignment="right"
        [format]="currencyFormat"
        dataField="valor"
        caption="Valor"
      ></dxi-column>

      <div *dxTemplate="let data of 'madeEffectiveColumn'">
        {{ customDisplay(data.value) }}
      </div>

      <dxi-column
        alignment="left"
        dataField="efetivado"
        width="100"
        caption="Efetivado"
      ><dxo-lookup [dataSource]="booleanData" valueExpr="key" displayExpr="value"></dxo-lookup></dxi-column>

      <dxi-column
        dataField="uuid"
        caption=""
        [allowFiltering]="false"
        [width]="digitalSignature ? 130 : 120"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'">
        <a
          [title]="data.data.efetivado === 'S' ? 'Efetivada' : 'Efetivar'"
          [ngStyle]="{'color': data.data.efetivado === 'S' ? '#63E6BE' : 'inherit'}"
          [ngClass]="{'icone-desabilitado': !(nivelPermissao === 'FULL' || nivelPermissao === 'EDITOR')}"
          (click)="data.data.efetivado === 'N' && effect(data.value)"
          class="dx-link dx-link-edit fas fa-check-circle dx-link-icon btn-icon-grid"
        >
        </a>
        <!-- <eqp-accounting-entry-link-button [numeros]="data.data?.numerosLancamentos"></eqp-accounting-entry-link-button> -->
        <a
          title="Editar"
          (click)="view(data.value)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          title="Imprimir"
          (click)="print(data)"
          class="dx-link dx-link-edit fas fa-print dx-link-icon btn-icon-grid"
        >
        </a>
        <eqp-acao-assinatura-digital
          [flagDocumentoAssinado]="data.data.assinado"
          [tipoMovimento]="'LIQUIDACAO'"
          [movimentoUuid]="data.value"
          (loading)="loading = $event"
        ></eqp-acao-assinatura-digital>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
