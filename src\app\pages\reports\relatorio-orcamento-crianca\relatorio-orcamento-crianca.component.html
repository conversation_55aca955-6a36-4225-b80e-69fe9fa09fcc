<eqp-standard-page
  [mainTitle]="tituloPagina"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [rightApproveButtonText]="'Confirmar'"
  [rightApproveButtonVisible]="true"
  [rightApproveButtonId]="'gerar-relatorio-orcamento-crianca'"
  [rightApproveButtonDisabled]="model.invalid"
  (rightApproveButtonEmitter)="submit()"
  [rightCustomButtonText]="'Baixar'"
  [rightCustomButtonVisible]="true"
  [rightCustomButtonId]="'gerar-relatorio-orcamento-crianca'"
  (rightCustomButtonEmitter)="downloadReport(model.getRawValue())"
  [rightCustomButtonDisabled]="model.invalid"
>
  <ng-container [formGroup]="model">
    <div class="container">
      <div class="row">
        <div class="col col-12 col-sm-6 col-md-3 col-xxl-2 mb-3">
          <eqp-field-date
            label="Data Inicial"
            formControlName="dataInicial"
            placeholder="dd/mm/aaaa"
            [required]="true"
          ></eqp-field-date>
        </div>
        <div class="col col-12 col-sm-6 col-md-3 col-xxl-2 mb-3">
          <eqp-field-date
            label="Data Final"
            formControlName="dataFinal"
            placeholder="dd/mm/aaaa"
            [required]="true"
          ></eqp-field-date>
        </div>
        <div class="col col-12 col-sm-6 col-md-3 col-xxl-2 mb-3">
          <eqp-nebular-select
            [size]="'small'"
            [shape]="'rectangle'"
            label="Ordenação"
            formControlName="ordenacao"
            [dataSource]="tiposOrdenacao"
            [displayExpr]="'valor'"
            valueExpr="chave"
          ></eqp-nebular-select>
        </div>
      </div>
      <div class="row">
        <div class="col col-12">
          <eqp-multiple-nebular-select
            placeholder="Selecionar entidade"
            label="Entidades"
            formControlName="entidadesUuid"
            [dataSource]="entidades"
          >
          </eqp-multiple-nebular-select>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col col-12 col-sm-6 col-md-3 col-xxl-2">
          <eqp-nebular-select
            [size]="'small'"
            [shape]="'rectangle'"
            label="Tipo de Arquivo"
            [formControl]="downloadType"
            [dataSource]="reportTypeList"
            [displayExpr]="'valor'"
            [valueExpr]="'chave'"
          ></eqp-nebular-select>
        </div>
      </div>
    </div>
  </ng-container>
</eqp-standard-page>


